import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "./ui/mode-toggle"
import { createClient } from "@/utils/supabase/server"
import Signout from "./sign-out"

export default async function Header() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  return (
    <header className="glass vercel-shadow sticky top-0 z-50 w-full">
      <div className="container mx-auto flex items-center justify-between px-4 py-4">
        {/* Left: Logo or Home */}
        <div className="flex items-center gap-8">
          <Link
            href="/"
            className="transition-smooth text-lg font-semibold tracking-tight hover:opacity-80"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              version="1.1"
              enableBackground="new 0 0 24 24"
              xmlSpace="preserve"
              className="h-6 w-6"
            >
              <g>
                <polygon fill="#009ADA" points="12,11 23,11 23,1 12,2" />
                <polygon fill="#009ADA" points="10,11 10,2.1818237 1,3 1,11" />
                <polygon fill="#FFFFFF" opacity="0.2" points="12,2 12,2.25 23,1.25 23,1" />
                <rect height="0.25" opacity="0.1" width="11" x="12" y="10.75" />
                <polygon
                  fill="#FFFFFF"
                  opacity="0.2"
                  points="1,3 1,3.25 10,2.4318237 10,2.1818237"
                />
                <rect height="0.25" opacity="0.1" width="9" x="1" y="10.75" />
                <polygon fill="#009ADA" points="12,13 23,13 23,23 12,22" />
                <polygon fill="#009ADA" points="10,13 10,21.8181763 1,21 1,13" />
                <polygon opacity="0.1" points="12,22 12,21.75 23,22.75 23,23" />
                <rect fill="#FFFFFF" height="0.25" opacity="0.2" width="11" x="12" y="13" />
                <polygon opacity="0.1" points="1,21 1,20.75 10,21.5681763 10,21.8181763" />
                <rect fill="#FFFFFF" height="0.25" opacity="0.2" width="9" x="1" y="13" />
                <defs>
                  <linearGradient
                    id="SVGID_1_"
                    gradientUnits="userSpaceOnUse"
                    x1="-0.0995096"
                    x2="25.6315994"
                    y1="5.3579059"
                    y2="17.3565197"
                  >
                    <stop offset="0" stopColor="#FFFFFF" stopOpacity="0.2" />
                    <stop offset="1" stopColor="#FFFFFF" stopOpacity="0" />
                  </linearGradient>
                </defs>
                <path
                  fill="url(#SVGID_1_)"
                  d="M12,2v9h11V1L12,2z M1,11h9V2.1818237L1,3V11z M12,22l11,1V13H12V22z M1,21l9,0.8181763V13H1V21z"
                />
              </g>
            </svg>
          </Link>
          <Link
            href="/private"
            className="text-muted-foreground hover:text-foreground transition-smooth text-sm"
          >
            <svg
    viewBox="0 0 64 64"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    aria-label="Unseen icon"
  >
    <g>
      <path
        fill="#d1e7ff"
        d="M61.44,33.82C55.35,42.33,43.23,47,32,47S8.65,42.33,2.56,33.82a3.26,3.26,0,0,1,0-3.65C8.65,21.61,20.77,16,32,16s23.35,5.61,29.44,14.17A3.24,3.24,0,0,1,61.44,33.82Z"
      />
      <circle fill="#e8f3ff" cx="32" cy="28" r="12" />
      <circle fill="#8bc4ff" cx="32" cy="28" r="5" />
      <path
        fill="#2e58ff"
        d="M63.07,29c-4.62-6.5-12.44-11.44-21-13.68L45.73,9a2,2,0,1,0-3.46-2l-4.36,7.49C23,12.17,8,19.09.93,29a5.25,5.25,0,0,0,0,6c3.93,5.5,10.43,9.73,18,12l-4.64,8a2,2,0,1,0,3.46,2l5.21-8.94C39.87,51.55,56,44.81,63.06,35A5.24,5.24,0,0,0,63.07,29ZM22,28a10,10,0,0,1,13.48-9.36l-9.92,17A10,10,0,0,1,22,28Zm16.87-7.24c7.59,7.2.31,19.9-9.8,16.8ZM4.19,32.66a1.27,1.27,0,0,1,0-1.33c3.6-5.06,9.65-9.13,16.38-11.39a13.94,13.94,0,0,0,3,19.18L21,43.45C13.9,41.44,7.72,37.6,4.19,32.66Zm55.62,0C53.9,40.92,39.79,47,25.09,44.39L27,41.07C40,46,51.32,31.1,43.43,19.94c6.73,2.26,12.78,6.33,16.38,11.39A1.25,1.25,0,0,1,59.81,32.66Z"
      />
    </g>
  </svg>
          </Link>
        </div>

        {/* Right: Login & Sign Up */}
        <div className="flex items-center gap-4">
          <ModeToggle />
          {!user ? (
            <>
              <Link href="/auth/login">
                <Button variant="ghost" size="sm" className="font-medium">
                  Login
                </Button>
              </Link>
              <Link href="/auth/signup">
                <Button variant="outline" size="sm" className="font-medium">
                  Sign Up
                </Button>
              </Link>
            </>
          ) : (
            <>
              <span className="text-muted-foreground text-sm font-medium">{user.email}</span>
              <Signout />
            </>
          )}
        </div>
      </div>
    </header>
  )
}
